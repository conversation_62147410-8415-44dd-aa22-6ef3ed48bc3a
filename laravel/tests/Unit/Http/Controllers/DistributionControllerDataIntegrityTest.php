<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\DistributionController;
use App\Services\Sales\Ceiling\DistributionService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Mockery;
use Tests\TestCase;

/**
 * Test data integrity for DistributionController
 * 
 * This test specifically addresses the bug where sales totals before and after
 * distribution processing are not matching for PRIVATE_PHARMACY distribution type.
 */
class DistributionControllerDataIntegrityTest extends TestCase
{
    private DistributionController $controller;
    private DistributionService $distributionService;
    private DistributionStrategyFactory $strategyFactory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->distributionService = Mockery::mock(DistributionService::class);
        $this->strategyFactory = Mockery::mock(DistributionStrategyFactory::class);
        
        $this->controller = new DistributionController(
            $this->distributionService,
            $this->strategyFactory
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test data integrity for PRIVATE_PHARMACY distribution with specific parameters
     * that were causing the sales mismatch issue.
     */
    public function test_private_pharmacy_distribution_maintains_sales_totals(): void
    {
        // Arrange - Create test data that reproduces the issue
        $ceilingSales = $this->createTestCeilingSalesData();
        $expectedTotalBefore = $ceilingSales->sum('number_of_values'); // 15000.00
        
        $request = Request::create('/distribution', 'POST', [
            'distribution_type' => DistributionType::PRIVATE_PHARMACY->value,
            'from_date' => '2025-01-01',
            'to_date' => '2025-01-31',
            'product_ids' => [],
            'distributor_ids' => [1, 2, 3, 4, 5]
        ]);

        // Mock the distribution service
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->once()
            ->with(
                DistributionType::PRIVATE_PHARMACY,
                '2025-01-01',
                '2025-01-31',
                [],
                [1, 2, 3, 4, 5]
            )
            ->andReturn($ceilingSales);

        // Mock the strategy factory and strategy
        $strategy = Mockery::mock(\App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategy::class);
        $strategy->shouldReceive('recalculateAndDistributeDifferences')
            ->once()
            ->with($ceilingSales)
            ->andReturn(true);

        $this->strategyFactory
            ->shouldReceive('create')
            ->once()
            ->with(DistributionType::PRIVATE_PHARMACY)
            ->andReturn($strategy);

        // Act
        $response = $this->controller->processDistribution($request);

        // Debug: Print response content if not 200
        if ($response->getStatusCode() !== 200) {
            echo "Response Status: " . $response->getStatusCode() . "\n";
            echo "Response Content: " . $response->getContent() . "\n";
        }

        // Assert
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['success']);
        $this->assertTrue($responseData['data']['integrity_check_passed']);
        $this->assertEquals($expectedTotalBefore, $responseData['data']['total_sales_before']);
        
        // The total after should equal total before (within rounding tolerance)
        $this->assertEqualsWithDelta(
            $responseData['data']['total_sales_before'],
            $responseData['data']['total_sales_after'],
            0.01,
            'Sales totals before and after distribution should match'
        );
    }

    /**
     * Test that empty ceiling sales returns appropriate response
     */
    public function test_empty_ceiling_sales_returns_success(): void
    {
        // Arrange
        $emptyCeilingSales = new Collection();
        
        $request = Request::create('/distribution', 'POST', [
            'distribution_type' => DistributionType::PRIVATE_PHARMACY->value,
            'from_date' => '2025-01-01',
            'to_date' => '2025-01-31',
            'product_ids' => [],
            'distributor_ids' => [1, 2, 3, 4, 5]
        ]);

        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->once()
            ->andReturn($emptyCeilingSales);

        // Act
        $response = $this->controller->processDistribution($request);

        // Assert
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('No ceiling sales found for the specified criteria', $responseData['message']);
    }

    /**
     * Create test ceiling sales data that reproduces the issue
     */
    private function createTestCeilingSalesData(): Collection
    {
        return new Collection([
            (object) [
                'id' => 1,
                'sale_ids' => '1,2,3',
                'number_of_units' => 150, // Exceeds limit of 100
                'number_of_values' => 7500.00,
                'number_of_bonus' => 15,
                'limit' => 100,
                'negative_limit' => -50,
                'distributor_id' => 1,
                'date' => '2025-01-15',
                'mapping_id' => 1
            ],
            (object) [
                'id' => 2,
                'sale_ids' => '4,5',
                'number_of_units' => 120, // Exceeds limit of 80
                'number_of_values' => 6000.00,
                'number_of_bonus' => 12,
                'limit' => 80,
                'negative_limit' => -40,
                'distributor_id' => 2,
                'date' => '2025-01-20',
                'mapping_id' => 2
            ],
            (object) [
                'id' => 3,
                'sale_ids' => '6',
                'number_of_units' => 90, // Exceeds limit of 60
                'number_of_values' => 1500.00,
                'number_of_bonus' => 9,
                'limit' => 60,
                'negative_limit' => -30,
                'distributor_id' => 3,
                'date' => '2025-01-25',
                'mapping_id' => 3
            ]
        ]);
    }

    /**
     * Test validation errors
     */
    public function test_validation_errors(): void
    {
        // Arrange
        $request = Request::create('/distribution', 'POST', [
            'distribution_type' => 999, // Invalid type
            'from_date' => 'invalid-date',
            'to_date' => '2025-01-31'
        ]);

        // Act & Assert
        $this->expectException(\Illuminate\Validation\ValidationException::class);
        $this->controller->processDistribution($request);
    }
}
