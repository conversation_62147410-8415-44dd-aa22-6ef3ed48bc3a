<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if we're using SQLite (for testing) or MySQL (for production)
        if (DB::connection()->getDriverName() === 'sqlite') {
            // SQLite doesn't support ENUM, so we'll just ensure the column exists
            // In SQLite, this would typically be handled with CHECK constraints
            // For testing purposes, we'll skip this migration
            return;
        }

        // MySQL-specific ENUM update
        DB::statement("ALTER TABLE `crm_widgets` CHANGE `type` `type` ENUM('table','pie','bar','tabs')");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
;